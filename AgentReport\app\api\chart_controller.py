"""Chart Selection API Controller

This module provides the FastAPI endpoints for chart selection and generation.
It handles chart query requests and returns appropriate chart configurations.
"""

import logging
from typing import Dict, Any

from fastapi import APIRouter, HTTPException, Depends, status

from app.models.chart import ChartQueryRequest, ChartQueryResponse
from app.models.user import User
from app.services.chart_service import ChartService
from app.utils.security import get_current_user

logger = logging.getLogger(__name__)

# Create router with prefix and tags
router = APIRouter(prefix="/api/chart", tags=["chart"])

# Initialize chart service
chart_service = ChartService()


@router.post("/query", response_model=ChartQueryResponse)
async def query_chart(
    request: ChartQueryRequest,
    current_user: User = Depends(get_current_user)
) -> ChartQueryResponse:
    """
    Process a chart query and return chart configuration and data.

    This endpoint analyzes a user's natural language query using LLM,
    determines the most appropriate chart type, and generates data either
    from a connected database (if database_id is provided) or mock data.

    Args:
        request: Chart query request containing the user's prompt and optional database_id
        current_user: Authenticated user from JWT token

    Returns:
        ChartQueryResponse with chart data or error information

    Raises:
        HTTPException: For authentication or validation errors
    """
    try:
        logger.info(f"Chart query request from user {current_user.id}: {request.prompt[:100]}...")

        # Add user context to request if not provided
        if not request.user_id:
            request.user_id = current_user.id

        # Log database usage
        if request.database_id:
            logger.info(f"Chart query will use database {request.database_id} for user {current_user.id}")
        else:
            logger.info(f"Chart query will use mock data for user {current_user.id}")
        
        # Process the chart query using the service
        response = await chart_service.process_chart_query(request)
        
        # Log the result
        if response.success:
            chart_type = response.data.chartType if response.data else "unknown"
            logger.info(f"Successfully generated {chart_type} chart for user {current_user.id}")
        else:
            logger.warning(f"Chart generation failed for user {current_user.id}: {response.error}")
        
        return response
        
    except ValueError as e:
        logger.error(f"Validation error in chart query: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid request: {str(e)}"
        )
    except Exception as e:
        logger.error(f"Unexpected error in chart query: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An unexpected error occurred while processing your chart request"
        )


@router.get("/types")
async def get_supported_chart_types(
    current_user: User = Depends(get_current_user)
) -> Dict[str, Any]:
    """
    Get list of supported chart types and their descriptions.
    
    Args:
        current_user: Authenticated user from JWT token
        
    Returns:
        Dictionary containing supported chart types and descriptions
    """
    try:
        from app.models.chart import ChartType, CHART_TYPE_DESCRIPTIONS
        
        chart_types = {
            "supported_types": [chart_type.value for chart_type in ChartType],
            "descriptions": {
                chart_type.value: CHART_TYPE_DESCRIPTIONS[chart_type] 
                for chart_type in ChartType
            },
            "default_colors": [
                '#8b5cf6', '#06b6d4', '#10b981', '#f59e0b', '#ef4444'
            ]
        }
        
        logger.info(f"Chart types requested by user {current_user.id}")
        return chart_types
        
    except Exception as e:
        logger.error(f"Error retrieving chart types: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve chart types"
        )


@router.post("/validate")
async def validate_chart_query(
    request: ChartQueryRequest,
    current_user: User = Depends(get_current_user)
) -> Dict[str, Any]:
    """
    Validate a chart query without generating the full chart.
    
    This endpoint can be used to check if a query is valid and get
    a preview of what chart type would be recommended.
    
    Args:
        request: Chart query request to validate
        current_user: Authenticated user from JWT token
        
    Returns:
        Validation result with recommended chart type
    """
    try:
        logger.info(f"Chart query validation from user {current_user.id}")
        
        # Basic validation
        if not request.prompt or len(request.prompt.strip()) == 0:
            return {
                "valid": False,
                "error": "Query prompt cannot be empty",
                "recommended_chart_type": None
            }
        
        if len(request.prompt) > 4000:
            return {
                "valid": False,
                "error": "Query prompt is too long (maximum 4000 characters)",
                "recommended_chart_type": None
            }
        
        # Get chart type recommendation without generating full data
        from app.models.chart import ChartGenerationContext
        context = ChartGenerationContext(
            user_query=request.prompt,
            user_id=request.user_id or current_user.id,
            dashboard_id=request.dashboard_id
        )
        
        recommendation = await chart_service._get_chart_type_recommendation(context)
        
        return {
            "valid": True,
            "recommended_chart_type": recommendation.chart_type.value,
            "confidence": recommendation.confidence,
            "reasoning": recommendation.reasoning,
            "alternative_types": [t.value for t in recommendation.alternative_types] if recommendation.alternative_types else []
        }
        
    except Exception as e:
        logger.error(f"Error validating chart query: {str(e)}")
        return {
            "valid": False,
            "error": f"Validation failed: {str(e)}",
            "recommended_chart_type": None
        }


@router.get("/health")
async def chart_service_health() -> Dict[str, Any]:
    """
    Health check endpoint for the chart service.
    
    Returns:
        Health status of the chart service
    """
    try:
        # This is a lightweight test - we don't need to process the full query
        health_status = {
            "status": "healthy",
            "service": "chart_selection",
            "timestamp": "2024-01-01T00:00:00Z",  # Would use actual timestamp
            "dependencies": {
                "bedrock_client": "available",
                "chart_service": "available"
            }
        }
        
        return health_status
        
    except Exception as e:
        logger.error(f"Chart service health check failed: {str(e)}")
        return {
            "status": "unhealthy",
            "service": "chart_selection",
            "error": str(e),
            "timestamp": "2024-01-01T00:00:00Z"
        }



