"use client";

import React, { useState, useCallback, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Loader2, X, MoreHorizontal, Send, AlertCircle, Sparkles, Database } from 'lucide-react';
import { ChartWidget as ChartWidgetType } from '@/types';
import { useApi } from '@/providers/ApiContext';
import ResponsiveChartRenderer from './ChartRenderer';
import CreateChartInput from './CreateChartInput';

interface ChartWidgetProps {
  widget: ChartWidgetType;
  onDelete: (widgetId: string) => void;
  onUpdate: (widgetId: string, updates: Partial<ChartWidgetType>) => void;
  className?: string;
}

const ChartWidget: React.FC<ChartWidgetProps> = ({
  widget,
  onDelete,
  onUpdate,
  className = '',
}) => {
  const { queryChart } = useApi();
  const [isEditing, setIsEditing] = useState(false);
  const [editPrompt, setEditPrompt] = useState('');
  const [isFocused, setIsFocused] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  const handleCreateChart = useCallback(async (prompt: string, databaseId?: string) => {
    onUpdate(widget.id, {
      isLoading: true,
      error: null,
      title: 'Generating...'
    });

    try {
      // Create the request with database_id if provided
      const request: any = { prompt };
      if (databaseId) {
        request.database_id = databaseId;
      }

      const response = await queryChart(request);

      if (response.success && response.data) {
        onUpdate(widget.id, {
          chartData: response.data,
          isLoading: false,
          error: null,
          title: response.data.title,
        });
      } else {
        throw new Error(response.error || 'Failed to generate chart');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to create chart';
      onUpdate(widget.id, {
        isLoading: false,
        error: errorMessage,
        title: 'Generation Failed',
      });
    }
  }, [widget.id, queryChart, onUpdate]);

  const handleEdit = useCallback(async (e: React.FormEvent) => {
    e.preventDefault();
    if (!editPrompt.trim()) return;

    // For edit mode, we'll use the same database as the original chart if available
    // or let the user select a new one through the input component
    await handleCreateChart(editPrompt.trim());
    setIsEditing(false);
    setEditPrompt('');
  }, [editPrompt, handleCreateChart]);

  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && (e.metaKey || e.ctrlKey)) {
      e.preventDefault();
      handleEdit(e as any);
    }
    if (e.key === 'Escape') {
      setIsEditing(false);
      setEditPrompt('');
    }
  }, [handleEdit]);

  // Remove chart click handler since we no longer need to open sidebar

  // handleConfigureChart removed - no longer needed since database selection is integrated

  const handleTextareaChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setEditPrompt(e.target.value);
    
    // Auto-resize textarea
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;
    }
  };

  // Auto-focus when entering edit mode
  useEffect(() => {
    if (isEditing && textareaRef.current) {
      textareaRef.current.focus();
    }
  }, [isEditing]);

  const isLoading = widget.isLoading;
  const error = widget.error;
  const hasChart = !!widget.chartData;
  const canSubmit = editPrompt.trim() && !isLoading;

  return (
    <Card 
      className={`h-full overflow-hidden chart-card ${className}`}
      style={{
        backgroundColor: 'var(--sidebar-surface-secondary)',
        borderColor: 'var(--sidebar-border)',
      }}
    >
      {/* Header */}
      <CardHeader
        className="flex flex-row items-center justify-between p-2 pb-1 react-grid-no-drag"
        onMouseDown={(e) => e.stopPropagation()}
      >
        <div></div>
        <div className="flex items-center gap-1">
          {hasChart && !isLoading && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsEditing(true)}
              className="h-6 w-6 p-0"
            >
              <MoreHorizontal className="h-3 w-3" />
            </Button>
          )}
        </div>
      </CardHeader>

      {/* Content */}
      <CardContent className="flex-1 h-full p-2">
        {/* Loading State */}
        {isLoading && (
          <div className="h-full flex flex-col items-center justify-center space-y-4">
            <div className="text-center space-y-4">
              <div className="text-lg font-medium text-foreground">Generating</div>
              <Loader2 className="h-8 w-8 animate-spin text-primary mx-auto" />
            </div>
          </div>
        )}

        {/* Edit Mode */}
        {isEditing && (
          <div 
            className="h-full flex flex-col items-center justify-center p-6 react-grid-no-drag" 
            onMouseDown={(e) => e.stopPropagation()}
          >
            <div className="w-full max-w-md space-y-4">
              <div className="text-center space-y-2">
                <h3 
                  className="text-base font-medium"
                  style={{ color: 'var(--sidebar-text-primary)' }}
                >
                  Modify your chart
                </h3>
                <p 
                  className="text-sm"
                  style={{ color: 'var(--sidebar-text-secondary)' }}
                >
                  Describe changes you want to make
                </p>
              </div>
              
              <form onSubmit={handleEdit} className="space-y-3">
                <div 
                  className={`relative rounded-xl border transition-all duration-200 ${
                    isFocused ? 'ring-2 ring-blue-500 ring-opacity-20' : ''
                  }`}
                  style={{
                    backgroundColor: 'var(--sidebar-bg)',
                    borderColor: isFocused ? 'rgba(59, 130, 246, 0.5)' : 'var(--sidebar-border)',
                  }}
                >
                  <Textarea
                    ref={textareaRef}
                    value={editPrompt}
                    onChange={handleTextareaChange}
                    onKeyDown={handleKeyDown}
                    onFocus={() => setIsFocused(true)}
                    onBlur={() => setIsFocused(false)}
                    placeholder="Change the chart type to bar chart..."
                    className="min-h-[80px] max-h-[120px] resize-none border-0 bg-transparent text-sm leading-relaxed placeholder:text-gray-500 focus:ring-0 focus:outline-none p-3 pr-12"
                    style={{ color: 'var(--sidebar-text-primary)' }}
                    maxLength={500}
                  />
                  
                  <div className="absolute bottom-2 right-2">
                    <Button
                      type="submit"
                      disabled={!canSubmit}
                      size="sm"
                      className={`h-7 w-7 p-0 rounded-lg border-0 transition-all duration-200 ${
                        canSubmit ? 'hover:scale-105' : 'opacity-50'
                      }`}
                      style={{
                        backgroundColor: canSubmit ? 'rgba(59, 130, 246, 1)' : 'var(--sidebar-surface-tertiary)',
                        color: canSubmit ? 'white' : 'var(--sidebar-text-tertiary)',
                      }}
                    >
                      <Send className="h-3 w-3" />
                    </Button>
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex gap-2">
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => {
                        setIsEditing(false);
                        setEditPrompt('');
                      }}
                      className="h-8 px-3 text-xs rounded-lg transition-all duration-200"
                      style={{ color: 'var(--sidebar-text-secondary)' }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.backgroundColor = 'transparent';
                      }}
                    >
                      Cancel
                    </Button>
                  </div>
                  <span 
                    className="text-xs"
                    style={{ color: 'var(--sidebar-text-tertiary)' }}
                  >
                    ⌘+Enter to send
                  </span>
                </div>
              </form>
            </div>
          </div>
        )}

        {/* Chart Display */}
        {hasChart && !isLoading && !error && !isEditing && widget.chartData && (
          <div className="h-full flex flex-col">
            {/* Chart Title */}
            {widget.chartData.title && (
              <div className="px-3 pb-2 pt-1 flex-shrink-0">
                <h3
                  className="text-sm font-medium text-center truncate leading-tight"
                  style={{ color: 'var(--sidebar-text-primary)' }}
                  title={widget.chartData.title} // Show full title on hover
                >
                  {widget.chartData.title}
                </h3>
              </div>
            )}

            {/* Chart Content */}
            <div className="flex-1 min-h-0">
              <ResponsiveChartRenderer
                key={`${widget.id}-${widget.layout.w}-${widget.layout.h}`}
                chartData={widget.chartData}
                className="h-full"
              />
            </div>
          </div>
        )}

        {/* Empty State - Chart Input */}
        {!hasChart && !isLoading && !error && !isEditing && (
          <div className="h-full flex items-center justify-center p-4">
            <div className="w-full max-w-md">
              <div className="text-center mb-4">
                <Database className="h-8 w-8 mx-auto text-gray-400 mb-2" />
                <h3 className="text-sm font-medium text-gray-700 mb-1">
                  Create Chart with Real Data
                </h3>
                <p className="text-xs text-gray-500">
                  Type your question to generate a chart from your database
                </p>
              </div>
              <CreateChartInput
                onSubmit={handleCreateChart}
                isLoading={isLoading}
                placeholder="e.g., Show me sales by region this month"
              />
            </div>
          </div>
        )}

        {/* Error State */}
        {error && !isLoading && !isEditing && (
          <div className="h-full flex flex-col items-center justify-center space-y-4">
            <div className="text-center space-y-2">
              <div className="text-destructive font-medium">Generation Failed</div>
              <p className="text-muted-foreground text-sm">{error}</p>
              <Button
                onClick={() => onUpdate(widget.id, { error: null })}
                variant="outline"
              >
                Try Again
              </Button>
            </div>
          </div>
        )}
      </CardContent>

    </Card>
  );
};

export default ChartWidget;
